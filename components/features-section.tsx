import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Battery, Smartphone, Car, Gauge } from "lucide-react"

export default function FeaturesSection() {
  const features = [
    {
      icon: Battery,
      title: "Advanced Battery Technology",
      description: "Blade Battery technology for enhanced safety and longevity",
    },
    {
      icon: Smartphone,
      title: "Smart Connectivity",
      description: "Seamless integration with your digital lifestyle",
    },
    {
      icon: Car,
      title: "Autonomous Driving",
      description: "Level 2+ autonomous driving capabilities",
    },
    {
      icon: Gauge,
      title: "Performance",
      description: "0-100km/h in just 3.8 seconds",
    },
  ]

  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Badge className="bg-orange-600 text-white mb-4">Innovation</Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Feature Packed</h2>
          <p className="text-gray-300 text-xl max-w-2xl mx-auto">
            Every BYD vehicle comes equipped with cutting-edge technology and premium features designed for the modern
            driver.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-lg bg-black/50">
              <feature.icon className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
              <p className="text-gray-400">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-3xl font-bold mb-6">Designed for Your Protection</h3>
            <p className="text-gray-300 mb-6">
              Our vehicles feature advanced safety systems, reinforced body structure, and intelligent driver assistance
              to keep you and your loved ones safe on every journey.
            </p>
            <Button className="bg-orange-600 hover:bg-orange-700">Safety Features</Button>
          </div>
          <div className="relative">
            <img src="/placeholder.svg?height=400&width=600" alt="Safety Features" className="rounded-lg shadow-xl" />
          </div>
        </div>
      </div>
    </section>
  )
}
