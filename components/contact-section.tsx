"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useEffect, useRef } from "react"

export default function ContactSection() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative py-20 overflow-hidden">
      {/* Video Background */}
      <video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" autoPlay muted loop playsInline>
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/70" />

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-2xl mx-auto text-center">
          <Badge className="bg-orange-600 text-white mb-4">Get in Touch</Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Experience the Future?</h2>
          <p className="text-gray-300 text-xl mb-12">
            Schedule your test drive today and discover what makes BYD the leader in electric mobility.
          </p>

          <form className="space-y-6 bg-black/50 p-8 rounded-lg backdrop-blur-sm">
            <div className="grid md:grid-cols-2 gap-4">
              <Input
                placeholder="First Name"
                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              />
              <Input
                placeholder="Last Name"
                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              />
            </div>
            <Input
              type="email"
              placeholder="Email Address"
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            />
            <Input
              placeholder="Phone Number"
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            />
            <Textarea
              placeholder="Message"
              rows={4}
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            />
            <Button className="w-full bg-orange-600 hover:bg-orange-700 text-lg py-3">Schedule Test Drive</Button>
          </form>
        </div>
      </div>
    </section>
  )
}
