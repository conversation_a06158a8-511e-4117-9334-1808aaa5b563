"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useEffect, useRef } from "react"

// Hero cho trang ATTO 3 - Ảnh 4
export default function Atto3Hero() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 to-black">
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover opacity-50"
        autoPlay
        muted
        loop
        playsInline
      >
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      <div className="absolute inset-0 bg-black/40" />

      <div className="container mx-auto px-6 relative z-10 pt-20">
        <div className="text-center max-w-4xl mx-auto">
          <Badge className="bg-orange-600 text-white mb-4">BYD ATTO 3</Badge>
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">Compact SUV Redefined</h1>
          <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
            The perfect balance of style, technology, and sustainability for the modern urban explorer.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-orange-600 hover:bg-orange-700 text-lg px-8 py-4">Configure Your ATTO 3</Button>
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black text-lg px-8 py-4 bg-transparent"
            >
              Book Test Drive
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
