"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"

const colorOptions = [
  { name: "Surf Blue", color: "bg-blue-500", image: "/placeholder.svg?height=400&width=600" },
  { name: "Cosmos Black", color: "bg-gray-900", image: "/placeholder.svg?height=400&width=600" },
  { name: "Boulder Grey", color: "bg-gray-600", image: "/placeholder.svg?height=400&width=600" },
]

export default function Atto3Gallery() {
  const [selectedColor, setSelectedColor] = useState(0)

  return (
    <section className="py-20 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Explore Colors</h2>
          <p className="text-gray-300 text-xl">Choose your perfect ATTO 3 color</p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <img
              src={colorOptions[selectedColor].image || "/placeholder.svg"}
              alt={`BYD ATTO 3 in ${colorOptions[selectedColor].name}`}
              className="w-full h-96 object-cover rounded-lg"
            />
          </div>

          <div className="flex justify-center space-x-4 mb-8">
            {colorOptions.map((option, index) => (
              <button
                key={index}
                className={`w-12 h-12 rounded-full border-4 ${
                  selectedColor === index ? "border-orange-500" : "border-gray-600"
                } ${option.color}`}
                onClick={() => setSelectedColor(index)}
              />
            ))}
          </div>

          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">{colorOptions[selectedColor].name}</h3>
            <Button className="bg-orange-600 hover:bg-orange-700">Configure This Color</Button>
          </div>
        </div>
      </div>
    </section>
  )
}
