export default function Atto3Specs() {
  const specs = [
    {
      category: "Performance",
      items: [
        { label: "Range", value: "480 km" },
        { label: "Acceleration", value: "7.3s (0-100km/h)" },
        { label: "Top Speed", value: "160 km/h" },
        { label: "Motor Power", value: "150 kW" },
      ],
    },
    {
      category: "Battery & Charging",
      items: [
        { label: "Battery Capacity", value: "60.48 kWh" },
        { label: "Battery Type", value: "LFP Blade Battery" },
        { label: "DC Fast Charging", value: "70 kW" },
        { label: "Charging Time", value: "45min (30-80%)" },
      ],
    },
    {
      category: "Dimensions",
      items: [
        { label: "Length", value: "4,455 mm" },
        { label: "Width", value: "1,875 mm" },
        { label: "Height", value: "1,615 mm" },
        { label: "Wheelbase", value: "2,720 mm" },
      ],
    },
  ]

  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Specifications</h2>
          <p className="text-gray-300 text-xl">Complete technical specifications</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {specs.map((section, index) => (
            <div key={index} className="bg-black/50 p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-6 text-orange-500">{section.category}</h3>
              <div className="space-y-4">
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex justify-between items-center border-b border-gray-700 pb-2">
                    <span className="text-gray-300">{item.label}</span>
                    <span className="font-semibold">{item.value}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
