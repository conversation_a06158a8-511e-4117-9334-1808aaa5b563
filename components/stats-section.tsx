export default function StatsSection() {
  const stats = [
    { number: "7+", label: "Years of Innovation" },
    { number: "30%", label: "Market Growth" },
    { number: "53,000", label: "Units Sold Globally" },
    { number: "500km", label: "Max Range" },
  ]

  return (
    <section className="py-16 bg-black">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-orange-500 mb-2">{stat.number}</div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
