"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useEffect, useRef } from "react"

export default function DolphinContact() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative py-24 overflow-hidden">
      <video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" autoPlay muted loop playsInline>
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      <div className="absolute inset-0 bg-black/70" />

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <Badge className="bg-orange-600 text-white px-6 py-3 text-lg mb-8">Contact</Badge>
          <h2 className="text-5xl lg:text-6xl font-bold mb-8">Ready to Drive Electric?</h2>
          <p className="text-gray-300 text-xl mb-16 leading-relaxed">
            Experience the BYD Dolphin today. Book your test drive and discover the future of urban mobility.
          </p>

          <form className="space-y-8 bg-black/60 backdrop-blur-sm p-12 rounded-3xl">
            <div className="grid md:grid-cols-2 gap-6">
              <Input
                placeholder="First Name"
                className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 h-14 text-lg"
              />
              <Input
                placeholder="Last Name"
                className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 h-14 text-lg"
              />
            </div>
            <Input
              type="email"
              placeholder="Email Address"
              className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 h-14 text-lg"
            />
            <Input
              placeholder="Phone Number"
              className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 h-14 text-lg"
            />
            <Button className="w-full bg-orange-600 hover:bg-orange-700 h-14 text-lg font-medium">
              Book Test Drive
            </Button>
          </form>
        </div>
      </div>
    </section>
  )
}
