import { Badge } from "@/components/ui/badge"

export default function DolphinFeatures() {
  return (
    <section className="py-24 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Badge className="bg-orange-600 text-white px-4 py-2 mb-6">Features</Badge>
          <h2 className="text-5xl lg:text-6xl font-bold mb-8">Feature Packed</h2>
        </div>

        {/* Features grid chính xác như ảnh */}
        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div>
            <h3 className="text-3xl font-bold mb-8">Advanced Technology</h3>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-3"></div>
                <div>
                  <h4 className="text-xl font-semibold mb-2">Blade Battery Technology</h4>
                  <p className="text-gray-400">Advanced LFP battery for enhanced safety and longevity</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-3"></div>
                <div>
                  <h4 className="text-xl font-semibold mb-2">15.6" Rotating Display</h4>
                  <p className="text-gray-400">Adaptive touchscreen with DiLink 4.0 system</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-3"></div>
                <div>
                  <h4 className="text-xl font-semibold mb-2">Smart Connectivity</h4>
                  <p className="text-gray-400">Seamless smartphone integration and OTA updates</p>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <img
              src="/placeholder.svg?height=500&width=600"
              alt="BYD Dolphin Features"
              className="w-full h-auto rounded-2xl shadow-2xl"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
