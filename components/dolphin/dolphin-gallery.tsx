"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

const galleryImages = [
  "/placeholder.svg?height=500&width=800",
  "/placeholder.svg?height=500&width=800",
  "/placeholder.svg?height=500&width=800",
  "/placeholder.svg?height=500&width=800",
]

export default function DolphinGallery() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % galleryImages.length)
  }

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + galleryImages.length) % galleryImages.length)
  }

  return (
    <section className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-5xl lg:text-6xl font-bold mb-8">Gallery</h2>
          <p className="text-gray-300 text-xl">Explore the BYD Dolphin from every angle</p>
        </div>

        <div className="relative max-w-6xl mx-auto">
          <div className="relative overflow-hidden rounded-2xl">
            <img
              src={galleryImages[currentIndex] || "/placeholder.svg"}
              alt={`BYD Dolphin Gallery ${currentIndex + 1}`}
              className="w-full h-[500px] object-cover"
            />
          </div>

          <Button
            variant="outline"
            size="icon"
            className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/50 border-white text-white hover:bg-white hover:text-black w-12 h-12"
            onClick={prevImage}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/50 border-white text-white hover:bg-white hover:text-black w-12 h-12"
            onClick={nextImage}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>

        <div className="flex justify-center mt-8 space-x-3">
          {galleryImages.map((_, index) => (
            <button
              key={index}
              className={`w-4 h-4 rounded-full transition-colors ${
                index === currentIndex ? "bg-orange-500" : "bg-gray-600"
              }`}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
