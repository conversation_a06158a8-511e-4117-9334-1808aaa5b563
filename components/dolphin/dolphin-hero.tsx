"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useEffect, useRef } from "react"

// Hero chính xác theo ảnh 2
export default function DolphinHero() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-900 via-gray-900 to-black">
      {/* Video Background */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover opacity-50"
        autoPlay
        muted
        loop
        playsInline
      >
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      <div className="absolute inset-0 bg-black/40" />

      <div className="container mx-auto px-6 relative z-10 pt-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content - chính xác theo <PERSON>nh */}
          <div>
            <div className="mb-6">
              <Badge className="bg-orange-600 text-white px-6 py-3 text-lg font-medium">BYD DOLPHIN</Badge>
            </div>

            <h1 className="text-6xl lg:text-8xl font-bold mb-8 leading-tight">
              Electric
              <br />
              Hatchback
            </h1>

            <p className="text-xl text-gray-200 mb-12 leading-relaxed max-w-lg">
              Compact, efficient, and perfect for urban mobility. The BYD Dolphin combines style with sustainability in
              a package designed for the modern city dweller.
            </p>

            {/* Buttons chính xác như ảnh */}
            <div className="flex gap-6">
              <Button className="bg-orange-600 hover:bg-orange-700 px-10 py-4 text-lg font-medium">
                Configure Now
              </Button>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-black px-10 py-4 text-lg font-medium bg-transparent"
              >
                Test Drive
              </Button>
            </div>
          </div>

          {/* Right Image - chính xác theo ảnh */}
          <div className="relative">
            <img src="/placeholder.svg?height=600&width=800" alt="BYD Dolphin" className="w-full h-auto" />
            {/* Floating specs như trong ảnh */}
            <div className="absolute bottom-8 right-8 bg-black/80 backdrop-blur-sm rounded-xl p-6">
              <div className="text-orange-500 text-2xl font-bold mb-2">405km</div>
              <div className="text-white text-sm">NEDC Range</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
