import { Badge } from "@/components/ui/badge"

export default function DolphinOverview() {
  return (
    <section className="py-24 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Badge className="bg-orange-600 text-white px-4 py-2 mb-6">Overview</Badge>
          <h2 className="text-5xl lg:text-6xl font-bold mb-8">Compact by Design</h2>
          <p className="text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed">
            The BYD Dolphin represents the perfect balance of efficiency, technology, and style. Designed for urban
            environments while delivering exceptional performance.
          </p>
        </div>

        {/* Key features grid chính xác như ảnh */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center p-8 bg-black/50 rounded-2xl">
            <div className="text-4xl font-bold text-orange-500 mb-4">405</div>
            <div className="text-white font-semibold mb-2">km Range</div>
            <div className="text-gray-400 text-sm">NEDC Standard</div>
          </div>
          <div className="text-center p-8 bg-black/50 rounded-2xl">
            <div className="text-4xl font-bold text-orange-500 mb-4">10.9</div>
            <div className="text-white font-semibold mb-2">seconds</div>
            <div className="text-gray-400 text-sm">0-100km/h</div>
          </div>
          <div className="text-center p-8 bg-black/50 rounded-2xl">
            <div className="text-4xl font-bold text-orange-500 mb-4">150</div>
            <div className="text-white font-semibold mb-2">km/h</div>
            <div className="text-gray-400 text-sm">Top Speed</div>
          </div>
          <div className="text-center p-8 bg-black/50 rounded-2xl">
            <div className="text-4xl font-bold text-orange-500 mb-4">60.48</div>
            <div className="text-white font-semibold mb-2">kWh</div>
            <div className="text-gray-400 text-sm">Battery Capacity</div>
          </div>
        </div>
      </div>
    </section>
  )
}
