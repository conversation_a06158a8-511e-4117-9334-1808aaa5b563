import { Button } from "@/components/ui/button"
import Link from "next/link"

export default function ModelsPreview() {
  const models = [
    {
      name: "BYD ATTO 3",
      image: "/placeholder.svg?height=300&width=500",
      href: "/atto3",
    },
    {
      name: "BYD M6",
      image: "/placeholder.svg?height=300&width=500",
      href: "/m6",
    },
    {
      name: "BYD Sealion 7",
      image: "/placeholder.svg?height=300&width=500",
      href: "/sealion7",
    },
    {
      name: "BYD Seal",
      image: "/placeholder.svg?height=300&width=500",
      href: "/seal",
    },
    {
      name: "BYD Dolphin",
      image: "/placeholder.svg?height=300&width=500",
      href: "/dolphin",
    },
  ]

  return (
    <section className="py-20 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Our Models</h2>
          <p className="text-gray-300 text-xl">Discover our complete range of electric vehicles</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {models.map((model, index) => (
            <Link key={index} href={model.href} className="group">
              <div className="bg-gray-900 rounded-lg overflow-hidden hover:bg-gray-800 transition-colors">
                <img
                  src={model.image || "/placeholder.svg"}
                  alt={model.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-4">{model.name}</h3>
                  <Button className="bg-orange-600 hover:bg-orange-700 w-full">Learn More</Button>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
