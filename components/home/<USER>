"use client"

import { Button } from "@/components/ui/button"
import Image from "next/image"

export default function ModelsShowcase() {
  const models = [
    {
      name: "BYD ATTO 3",
      subtitle: "Best Selling Car in Singapore",
      description: "The perfect blend of style, technology, and sustainability for urban adventures.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FAtto%20Interior%2Fatto-2025%2FUntitled%20(4)%20(1).webp?alt=media&token=fc29831d-49d7-4666-afba-20c0fa2c055e",
      priceFrom: "$164,388",
      priceNote: "Incl COE",
      monthlyFrom: "$1,378",
      monthlyNote: "Per Month",
      features: ["420km Range", "Fast Charging", "Smart Interior"],
      isNewArrival: true,
    },
    {
      name: "BYD HAN",
      subtitle: "Executive Sedan",
      description: "Sophisticated design with cutting-edge electric technology and luxury comfort.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FM6%2F%E4%BA%9A%E5%A4%AA%E5%AE%8BMax%20KV%E8%B7%AF%E8%B7%91%E7%AF%87%20(1).webp?alt=media&token=9b53c1f7-2991-4c07-99f2-ee47b424dfde",
      priceFrom: "$185,000",
      priceNote: "Incl COE",
      monthlyFrom: "$1,580",
      monthlyNote: "Per Month",
      features: ["605km Range", "Luxury Interior", "Dragon Face Design"],
      isNewArrival: false,
    },
    {
      name: "BYD TANG",
      subtitle: "7-Seater SUV",
      description: "Spacious family SUV with all-wheel drive and premium comfort features.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2Fsealion%2Fgeneral%2F20240918%E4%BA%9A%E5%A4%AA%E6%B5%B7%E7%8B%AE07%E5%8D%95%E8%BD%A6%E8%B7%AF%E8%B7%91KV.webp?alt=media&token=cddc34a4-b19d-4bef-ae3a-45dad85afffc",
      priceFrom: "$175,000",
      priceNote: "Incl COE",
      monthlyFrom: "$1,480",
      monthlyNote: "Per Month",
      features: ["505km Range", "All-Wheel Drive", "7 Seats"],
      isNewArrival: false,
    },
    {
      name: "BYD SEAL",
      subtitle: "Sports Sedan",
      description: "Dynamic performance meets elegant design in this premium electric sedan.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FSeal%2Fkv%2Fbyd%20%E8%B5%9B%E9%81%93kv%2099%20RGB%20x.webp?alt=media&token=309710af-74f9-4e79-a31e-8fd4599fe83c",
      priceFrom: "$168,000",
      priceNote: "Incl COE",
      monthlyFrom: "$1,420",
      monthlyNote: "Per Month",
      features: ["550km Range", "Sports Performance", "Premium Design"],
      isNewArrival: false,
    },
    {
      name: "BYD DOLPHIN",
      subtitle: "Compact Hatchback",
      description: "Agile, efficient, and perfect for city driving with smart technology.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FHome%2Fdolphin-edit%2016-9%20ratio%20copy%20(2)%20(1).webp?alt=media&token=5ef0d164-ec00-4474-8557-59eb5c2d4655",
      priceFrom: "$145,000",
      priceNote: "Incl COE",
      monthlyFrom: "$1,250",
      monthlyNote: "Per Month",
      features: ["405km Range", "City Driving", "Smart Tech"],
      isNewArrival: false,
    },
  ]

  return (
    <>
      {models.map((model, index) => (
        <section key={index} className="relative h-screen flex items-center justify-center overflow-hidden">
          {/* Background Image */}
          <div className="absolute inset-0">
            <Image
              src={model.image}
              alt={model.name}
              fill
              className="object-cover"
              onError={(e) => {
                e.currentTarget.src = "/placeholder-car.jpg"
              }}
            />
          </div>

          {/* Dark Overlay with gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/40 to-transparent" />

          {/* New Arrival Badge - Top Left */}
          {model.isNewArrival && (
            <div className="absolute top-8 left-1/2 transform -translate-x-1/2 z-20">
              <div className="bg-orange-600 text-white px-6 py-2 rounded-full text-sm font-bold">
                New Arrival
              </div>
            </div>
          )}

          {/* Content Layout - Vertical like in the image */}
          <div className="relative z-10 w-full max-w-4xl mx-auto px-6 text-center">

            {/* Top Section - Title and Subtitle */}
            <div className="mb-8">
              <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-4 leading-tight">
                {model.name}
              </h2>

              <p className="text-xl md:text-2xl text-white font-medium">
                {model.subtitle}
              </p>
            </div>

            {/* Middle Section - Car Image Space (handled by background) */}
            <div className="h-64 md:h-80 lg:h-96 mb-8">
              {/* This space is for the car image which is in the background */}
            </div>

            {/* Bottom Section - Pricing and Buttons */}
            <div className="space-y-6">
              {/* Pricing Section */}
              <div className="flex flex-col sm:flex-row justify-center items-center gap-8 mb-8">
                <div className="flex items-baseline gap-2">
                  <span className="text-lg text-gray-300">From</span>
                  <span className="text-3xl md:text-4xl font-bold text-white">{model.priceFrom}</span>
                  <span className="text-lg text-gray-300">{model.priceNote}</span>
                </div>
                <div className="hidden sm:block w-px h-8 bg-gray-500"></div>
                <div className="flex items-baseline gap-2">
                  <span className="text-lg text-gray-300">From</span>
                  <span className="text-3xl md:text-4xl font-bold text-white">{model.monthlyFrom}</span>
                  <span className="text-lg text-gray-300">{model.monthlyNote}</span>
                </div>
              </div>

              {/* Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-white px-10 py-4 text-lg font-medium rounded-full">
                  Learn More
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-black px-10 py-4 text-lg font-medium bg-transparent rounded-full"
                >
                  Test Drive
                </Button>
              </div>
            </div>
          </div>
        </section>
      ))}
    </>
  )
}
