"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useEffect, useRef, useState } from "react"

export default function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [videoLoaded, setVideoLoaded] = useState(false)
  const [videoError, setVideoError] = useState(false)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedData = () => {
      setVideoLoaded(true)
      video.play().catch((error) => {
        console.error("Video autoplay failed:", error)
        setVideoError(true)
      })
    }

    const handleError = () => {
      console.error("Video failed to load")
      setVideoError(true)
    }

    const handleCanPlay = () => {
      video.play().catch((error) => {
        console.error("Video play failed:", error)
      })
    }

    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('error', handleError)
    video.addEventListener('canplay', handleCanPlay)

    if (video.readyState >= 3) {
      handleLoadedData()
    }

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('error', handleError)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">

      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        onLoadedData={() => setVideoLoaded(true)}
        onError={() => setVideoError(true)}
      >
        <source src="https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2Fvideos%2Fatto-vid-bg-desktop.mp4?alt=media&token=d296b363-be22-4e8b-9ce3-886fa8618a44" type="video/mp4" />
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      {videoError && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800" />
      )}

      {!videoLoaded && !videoError && (
        <div className="absolute inset-0 bg-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
        </div>
      )}

      <div className="absolute inset-0 bg-black/40" />

      <div className="relative z-10 w-full h-full flex items-center">
        <div className="w-full max-w-screen-xl mx-auto px-6 lg:px-8">
          <div className="text-center md:text-left max-w-none md:max-w-2xl lg:max-w-3xl">
            <div className="inline-block bg-orange-600/20 backdrop-blur-sm border border-orange-600/30 text-orange-400 px-4 py-2 rounded-full text-sm font-medium mb-6">
              CAR OWNERSHIP BEYOND THE DRIVE
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight text-white">
              It's More Than Just a Car
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-lg mx-auto md:mx-0">
              Experience a new dimension of ownership with BYD, where being a car owner extends beyond the drive.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
              <Button size="lg" className="bg-white hover:bg-gray-100 text-black px-8 py-3 text-base font-medium rounded-md">
                Book a Table
              </Button>
              <Button
                size="lg"
                className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 text-base font-medium rounded-md"
              >
                Book a Test Drive →
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
