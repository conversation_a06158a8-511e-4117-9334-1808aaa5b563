import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export default function ExperienceSection() {
  return (
    <section className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Image - chính xác theo <PERSON> */}
          <div className="relative order-2 lg:order-1">
            <img
              src="/placeholder.svg?height=600&width=700"
              alt="BYD Interior Experience"
              className="w-full h-auto rounded-2xl shadow-2xl"
            />
            {/* Floating stats như trong ảnh */}
            <div className="absolute -bottom-8 -right-8 bg-orange-600 rounded-2xl p-6 shadow-2xl">
              <div className="text-white text-3xl font-bold mb-2">480km</div>
              <div className="text-orange-100 text-sm">Max Range</div>
            </div>
          </div>

          {/* Right Content - chính xác theo <PERSON> */}
          <div className="order-1 lg:order-2">
            <div className="mb-6">
              <Badge className="bg-green-600 text-white px-4 py-2 text-sm font-medium">Experience</Badge>
            </div>

            <h2 className="text-5xl lg:text-6xl font-bold mb-8 leading-tight">
              Elevating Your Experience
              <br />
              Beyond Just Owning a Car
            </h2>

            <p className="text-gray-300 text-lg mb-12 leading-relaxed">
              Experience a new level of automotive luxury with our advanced technology, premium materials, and
              innovative features designed to enhance every journey you take.
            </p>

            {/* Stats row chính xác như ảnh */}
            <div className="grid grid-cols-3 gap-8 mb-12">
              <div>
                <div className="text-4xl font-bold text-orange-500 mb-2">7+</div>
                <div className="text-gray-400 text-sm uppercase tracking-wider">Years Innovation</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-orange-500 mb-2">30%</div>
                <div className="text-gray-400 text-sm uppercase tracking-wider">Market Growth</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-orange-500 mb-2">53,000</div>
                <div className="text-gray-400 text-sm uppercase tracking-wider">Units Sold</div>
              </div>
            </div>

            {/* Buttons chính xác như ảnh */}
            <div className="flex gap-6">
              <Button className="bg-orange-600 hover:bg-orange-700 px-8 py-3">Discover More</Button>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-black px-8 py-3 bg-transparent"
              >
                Book Test Drive
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
