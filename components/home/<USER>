"use client"

import { Button } from "@/components/ui/button"
import Image from "next/image"

export default function InnovationSection() {
  const innovations = [
    {
      title: "Blade Battery Technology",
      description: "Revolutionary battery technology that's safer, more durable, and longer-lasting than traditional lithium-ion batteries.",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop",
      features: ["Ultra-Safe", "Long Life", "Fast Charging"]
    },
    {
      title: "Dragon Face Design",
      description: "Our signature design language that combines elegance with aerodynamic efficiency for the perfect balance of form and function.",
      image: "https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=600&h=400&fit=crop",
      features: ["Aerodynamic", "Elegant", "Distinctive"]
    },
    {
      title: "Smart Connectivity",
      description: "Advanced infotainment and connectivity features that keep you connected and in control wherever you go.",
      image: "https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=600&h=400&fit=crop",
      features: ["AI Assistant", "OTA Updates", "Smart Home Integration"]
    }
  ]

  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Innovation That Drives Us Forward
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover the cutting-edge technologies that make BYD vehicles the future of sustainable mobility.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {innovations.map((innovation, index) => (
            <div key={index} className="bg-black rounded-2xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
              <div className="relative h-48">
                <Image
                  src={innovation.image}
                  alt={innovation.title}
                  fill
                  className="object-cover"
                />
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold text-white mb-4">{innovation.title}</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">{innovation.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {innovation.features.map((feature, idx) => (
                    <span key={idx} className="bg-orange-600/20 text-orange-400 px-3 py-1 rounded-full text-sm">
                      {feature}
                    </span>
                  ))}
                </div>

                <Button variant="outline" className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white w-full">
                  Learn More
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
