"use client"

import { useEffect, useRef } from "react"

// Hero cho trang Models - Ảnh 3
export default function ModelsHero() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      <video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" autoPlay muted loop playsInline>
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      <div className="absolute inset-0 bg-black/60" />

      <div className="relative z-10 text-center max-w-4xl mx-auto px-6 pt-20">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">Our Electric Vehicle Range</h1>
        <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto">
          Discover the perfect electric vehicle for your lifestyle from our comprehensive model lineup.
        </p>
      </div>
    </section>
  )
}
