"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useEffect, useRef } from "react"

const vehicles = [
  {
    name: "BYD ATTO 3",
    subtitle: "Compact SUV",
    description: "The perfect blend of style, technology, and sustainability for urban adventures.",
    image: "/placeholder.svg?height=400&width=600",
    color: "bg-blue-900",
  },
  {
    name: "BYD M6",
    subtitle: "Family MPV",
    description: "Spacious, comfortable, and efficient for all your family journeys.",
    image: "/placeholder.svg?height=400&width=600",
    color: "bg-gray-800",
  },
  {
    name: "BYD Sealion 7",
    subtitle: "Premium SUV",
    description: "Luxury meets performance in this flagship electric SUV.",
    image: "/placeholder.svg?height=400&width=600",
    color: "bg-blue-800",
  },
  {
    name: "BYD Seal",
    subtitle: "Executive Sedan",
    description: "Sophisticated design with cutting-edge electric technology.",
    image: "/placeholder.svg?height=400&width=600",
    color: "bg-gray-900",
  },
  {
    name: "BYD Dolphin",
    subtitle: "Compact Hatchback",
    description: "Agile, efficient, and perfect for city driving.",
    image: "/placeholder.svg?height=400&width=600",
    color: "bg-slate-800",
  },
]

export default function VehicleShowcase() {
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])

  useEffect(() => {
    videoRefs.current.forEach((video) => {
      if (video) {
        video.play().catch(console.error)
      }
    })
  }, [])

  return (
    <section className="py-20">
      {vehicles.map((vehicle, index) => (
        <div key={index} className={`relative min-h-screen flex items-center ${vehicle.color}`}>
          {/* Video Background for some sections */}
          {index % 2 === 0 && (
            <>
              <video
                ref={(el) => (videoRefs.current[index] = el)}
                className="absolute inset-0 w-full h-full object-cover opacity-30"
                autoPlay
                muted
                loop
                playsInline
              >
                <source src="/placeholder-video.mp4" type="video/mp4" />
              </video>
              <div className="absolute inset-0 bg-black/40" />
            </>
          )}

          <div className="container mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className={index % 2 === 0 ? "order-1" : "order-2"}>
                <Badge className="bg-orange-600 text-white mb-4">{vehicle.subtitle}</Badge>
                <h2 className="text-5xl md:text-6xl font-bold mb-6">{vehicle.name}</h2>
                <p className="text-gray-300 text-xl mb-8 max-w-lg">{vehicle.description}</p>
                <div className="flex gap-4">
                  <Button className="bg-orange-600 hover:bg-orange-700">Learn More</Button>
                  <Button
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-black bg-transparent"
                  >
                    Test Drive
                  </Button>
                </div>
              </div>
              <div className={index % 2 === 0 ? "order-2" : "order-1"}>
                <img
                  src={vehicle.image || "/placeholder.svg"}
                  alt={vehicle.name}
                  className="w-full h-auto rounded-lg shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      ))}
    </section>
  )
}
